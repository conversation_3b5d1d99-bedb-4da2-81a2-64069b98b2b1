<?php

namespace Tests\Feature;

use App\Events\UserDeleted;
use App\Models\User;
use App\Models\SocialProvider;
use App\Models\UserAgentPreference;
use App\Models\ChatSession;
use App\Models\MessageReaction;
use App\Models\MessageReport;
use App\Models\Subscription;
use App\Models\PaymentHistory;
use App\Models\Recommendation;
use App\Models\FeatureUsageStatistic;
use App\Models\DeviceInfo;
use App\Models\PlaceFavorite;
use App\Models\UserRecommendationUsage;
use App\Models\Agent;
use App\Models\ChatMessage;
use App\Models\Place;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class SoftDeleteUserRelatedDataTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_deletion_performs_soft_deletes_on_all_related_models()
    {
        // Create a user
        $user = User::factory()->create();
        
        // Create related data for all models
        $socialProvider = SocialProvider::factory()->create(['user_id' => $user->id]);
        
        $agent = Agent::factory()->create();
        $userAgentPreference = UserAgentPreference::factory()->create([
            'user_id' => $user->id,
            'agent_id' => $agent->id
        ]);
        
        $chatSession = ChatSession::factory()->create(['user_id' => $user->id]);
        
        $chatMessage = ChatMessage::factory()->create([
            'user_id' => $user->id,
            'chat_session_id' => $chatSession->id,
            'agent_id' => $agent->id
        ]);
        
        $messageReaction = MessageReaction::factory()->create([
            'user_id' => $user->id,
            'chat_message_id' => $chatMessage->id,
            'agent_id' => $agent->id
        ]);
        
        $messageReport = MessageReport::factory()->create([
            'user_id' => $user->id,
            'chat_message_id' => $chatMessage->id,
            'agent_id' => $agent->id
        ]);
        
        $subscription = Subscription::factory()->create(['user_id' => $user->id]);
        
        $paymentHistory = PaymentHistory::factory()->create([
            'user_id' => $user->id,
            'subscription_id' => $subscription->id
        ]);
        
        $recommendation = Recommendation::factory()->create([
            'user_id' => $user->id,
            'chat_message_id' => $chatMessage->id,
            'agent_id' => $agent->id
        ]);
        
        $featureUsageStatistic = FeatureUsageStatistic::factory()->create(['user_id' => $user->id]);
        
        $deviceInfo = DeviceInfo::factory()->create(['user_id' => $user->id]);
        
        $place = Place::factory()->create();
        $placeFavorite = PlaceFavorite::factory()->create([
            'user_id' => $user->id,
            'place_id' => $place->id
        ]);
        
        $userRecommendationUsage = UserRecommendationUsage::factory()->create(['user_id' => $user->id]);

        // Fire the UserDeleted event
        $newEmail = 'deleted_' . time() . '_' . $user->email;
        Event::dispatch(new UserDeleted($user, $newEmail));

        // Verify that all records are soft deleted (exist in database but have deleted_at timestamp)
        $this->assertSoftDeleted('users', ['id' => $user->id]);
        $this->assertSoftDeleted('social_providers', ['id' => $socialProvider->id]);
        $this->assertSoftDeleted('user_agent_preferences', ['id' => $userAgentPreference->id]);
        $this->assertSoftDeleted('chat_sessions', ['id' => $chatSession->id]);
        $this->assertSoftDeleted('message_reactions', ['id' => $messageReaction->id]);
        $this->assertSoftDeleted('message_reports', ['id' => $messageReport->id]);
        $this->assertSoftDeleted('subscriptions', ['id' => $subscription->id]);
        $this->assertSoftDeleted('payment_history', ['id' => $paymentHistory->id]);
        $this->assertSoftDeleted('recommendations', ['id' => $recommendation->id]);
        $this->assertSoftDeleted('feature_usage_statistics', ['id' => $featureUsageStatistic->id]);
        $this->assertSoftDeleted('device_info', ['id' => $deviceInfo->id]);
        $this->assertSoftDeleted('place_favorites', ['id' => $placeFavorite->id]);
        $this->assertSoftDeleted('user_recommendation_usage', ['id' => $userRecommendationUsage->id]);

        // Verify that the user's email was updated
        $user->refresh();
        $this->assertEquals($newEmail, $user->email);
    }

    public function test_soft_deleted_records_are_not_retrieved_by_default()
    {
        // Create a user and related data
        $user = User::factory()->create();
        $socialProvider = SocialProvider::factory()->create(['user_id' => $user->id]);

        // Verify records exist before deletion
        $this->assertDatabaseHas('users', ['id' => $user->id]);
        $this->assertDatabaseHas('social_providers', ['id' => $socialProvider->id]);

        // Fire the UserDeleted event
        $newEmail = 'deleted_' . time() . '_' . $user->email;
        Event::dispatch(new UserDeleted($user, $newEmail));

        // Verify that soft deleted records are not retrieved by default queries
        $this->assertNull(User::find($user->id));
        $this->assertNull(SocialProvider::find($socialProvider->id));

        // But they can be retrieved with withTrashed()
        $this->assertNotNull(User::withTrashed()->find($user->id));
        $this->assertNotNull(SocialProvider::withTrashed()->find($socialProvider->id));
    }
}
